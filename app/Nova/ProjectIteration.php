<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON><PERSON>\Nova\Fields\Textarea;
use <PERSON><PERSON>\Nova\Fields\DateTime;
use <PERSON><PERSON>\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\File;

use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\MorphedByMany;
use Laravel\Nova\Fields\BelongsToMany;
use Laravel\Nova\Fields\Boolean;

use Vizar\CompiledMapCard\CompiledMapCard;

class ProjectIteration extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\ProjectIteration>
     */
    public static $model = \App\Models\ProjectIteration::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'name', 'project.name'
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->onlyOnDetail(),
            Text::make('Iteration Name', 'name')->showWhenPeeking(),
            Textarea::make('Description', 'description')->alwaysShow()->showWhenPeeking(),
            Select::make('Altitude Provider', 'altitude_source')->options([
                'Google Earth' => 'Google Earth',
                'ArcGIS' => 'ArcGIS',
            ]),
            DateTime::make('Date Created', 'created_at')->sortable()->onlyOnIndex()->showOnDetail(),
            DateTime::make('Date Modified', 'updated_at')->onlyOnDetail()->filterable(),
            BelongsTo::make('Project')->sortable(),
            HasMany::make('Files', 'projectIterationFiles', 'App\Nova\projectIterationFile'),
            MorphedByMany::make('Model Placement', 'ClientModels', ClientModel::class)
                ->fields(function ($request, $relatedModel) {
                    return [
                        Text::make('Placement Label', 'placement_label')->help(
                            'This will appear in the mobile application as Asset Label'
                        ),
                        Text::make('Placement Comment', 'placement_comment')->help(
                            'This will appear when an Asset Pin is selected and the information pane expands'
                        ),
                        Number::make('Latitude', 'latitude')->min(-90)->max(90)->step(0.00000001)->help(
                            'Min: -90 Max: 90 Precision: 0.00000001'
                        ),
                        Number::make('Longitude', 'longitude')->min(-180)->max(180)->step(0.00000001)->help(
                            'Min: -180 Max: 180 Precision: 0.00000001'
                        ),
                        Number::make('Altitude', 'altitude')->min(-5000.000)->max(20000.000)->step(0.01)->help(
                            'In: Meters Min: -5,000 Max: 20,000 Precision: 0.01'
                        ),
                        Number::make('Vertical Offset', 'vertical_offset')->min(-9999.00)->max(9999.99)->step(0.01)->help(
                            'In: Meters Min: -9999.99 Max: 9999.99 Precision: 0.01'
                        ),
                        Number::make('Orientation', 'orientation')->min(0)->max(359.99)->step(0.01)->help(
                            'In: Degrees Min: 0 Max: 359.99 Precision: 0.01'
                        ),
                        Number::make('Gradient', 'gradient')->min(-10)->max(10)->step(0.0001)->help(
                            'Min: -10 Max: 10 Precision: 0.0001'
                        ),
                        Number::make('Group Number', 'group')->help(
                            'Set a group number for this placement. This is used to group a number of assets together for sequence placement logic. Should contain the same number for a group. <br>Example: Power towers when designing Powerlines'
                        ),
                        Number::make('Group Sequence', 'group_sequence')->help(
                            'Provide a numerical sequence inside a Group. Works with the above Group Number field'
                        ),
                    ];
                })->allowDuplicateRelations()->searchable(),
                HasMany::make('Vantage Points', 'vantagePoints'),
                HasMany::make('Files', 'projectIterationFiles', 'App\Nova\projectIterationFile'),
                HasMany::make('Maps')
        ];
    }

    /**
    * Build an "index" query for the given resource.
    *
    * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
    * @param  \Illuminate\Database\Eloquent\Builder  $query
    * @return \Illuminate\Database\Eloquent\Builder
    */
    public static function indexQuery(NovaRequest $request, $query)
    {
        switch ($request->user()->role->name)
        {
            case 'Global Administrator':
                return $query;
                break;

            case 'Client Administrator':
                return $query
                    ->join('projects', 'project_iterations.project_id', '=', 'projects.id')
                    ->select('project_iterations.*', 'client_id')
                    ->where('client_id', $request->user()->client->id);
                break;

            case $request->user()->role->name == 'Project Administrator' || $request->user()->role->name == 'Project Viewer':
                return $query->where('project_id', $request->user()->target_project_id);
                break;

            case $request->user()->role->name == 'Iteration Administrator' || $request->user()->role->name == 'Iteration Viewer':
                return $query->where('project_iterations.id', $request->user()->target_iteration_id);
                break;

            default:
                # moo - only grass here
                break;
        }
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [
            (new CompiledMapCard)
                ->onlyOnDetail()
                ->width('full'),
        ];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [
            // Actions can be added here
        ];
    }
}
