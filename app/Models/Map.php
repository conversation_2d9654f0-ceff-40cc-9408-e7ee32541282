<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

use MatanYadaev\EloquentSpatial\Traits\HasSpatial;
use App\Traits\HasObfuscatedId;

class Map extends Model
{
    use HasFactory;
    use HasObfuscatedId;

    protected $fillable = [
        'name',
        'description',
        'project_iteration_id',
        'is_main'
    ];

    protected $casts = [
        'is_main' => 'boolean'
    ];

    public function projectIteration(): BelongsTo
    {
        return $this->BelongsTo(ProjectIteration::class);
    }

    public function mapPoints(): HasMany
    {
        return $this->HasMany(MapPoint::class);
    }

    public function mapAreas(): HasMany
    {
        return $this->HasMany(MapArea::class);
    }

    public function mapMultiAreas(): Has<PERSON>any
    {
        return $this->HasMany(MapMultiArea::class);
    }

    /**
     * Boot the model and add event listeners
     */
    protected static function boot()
    {
        parent::boot();

        // When saving a map as main, ensure no other maps in the same iteration are main
        static::saving(function ($map) {
            if ($map->is_main && $map->project_iteration_id) {
                // Set all other maps in this iteration to not main
                static::where('project_iteration_id', $map->project_iteration_id)
                    ->where('id', '!=', $map->id)
                    ->update(['is_main' => false]);
            }
        });
    }

    /**
     * Scope to get only main maps
     */
    public function scopeMain($query)
    {
        return $query->where('is_main', true);
    }
}
