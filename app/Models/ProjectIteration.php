<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

class ProjectIteration extends Model
{
    use HasFactory;

    public function project(): BelongsTo
    {
        return $this->BelongsTo(Project::class);
    }

    public function clientModels(): MorphToMany
    {
        return $this->morphedByMany(ClientModel::class, 'iteration_asset')
            ->withPivot
            (
                'id',
                'placement_label',
                'placement_comment',
                'latitude',
                'longitude',
                'altitude',
                'vertical_offset',
                'orientation',
                'gradient',
                'group',
                'group_sequence'
            )->using(IterationAsset::class);
    }

    public function libraryModels(): MorphToMany
    {
        return $this->morphedByMany(LibraryModel::class, 'iteration_assets');
    }

    public function user(): HasOne
    {
        return $this->HasOne(User::class);
    }

    public function projectIterationFiles(): Has<PERSON>any
    {
        return $this-><PERSON><PERSON><PERSON>(ProjectIterationFile::class);
    }

    public function vantagePoints(): HasMany
    {
        return $this->hasMany(VantagePoint::class);
    }

    public function maps(): HasMany
    {
        return $this->HasMany(Map::class);
    }

    /**
     * Get the main map for this iteration
     */
    public function mainMap()
    {
        return $this->hasOne(Map::class)->where('is_main', true);
    }

    /**
     * Get the main map or the first map if no main map is set
     */
    public function getMainMapAttribute()
    {
        $mainMap = $this->mainMap()->first();

        if (!$mainMap) {
            $mainMap = $this->maps()->first();
        }

        return $mainMap;
    }
}
