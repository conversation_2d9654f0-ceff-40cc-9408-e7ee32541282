<template>
  <Card class="overflow-hidden">
    <div class="flex flex-col h-full">
      <div class="px-3 py-2 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
        <h1 class="text-lg font-medium text-gray-700">Map</h1>
        <div v-if="loading" class="text-sm text-gray-500 flex items-center">
          <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Loading map data...
        </div>
      </div>
      <div v-if="error" class="bg-red-50 border-l-4 border-red-400 p-4 text-red-700">
        {{ error }}
      </div>
      <div ref="mapContainer" class="flex-grow" style="min-height: 500px;"></div>
    </div>
  </Card>
</template>

<script>
export default {
  props: [
    'card',
    'resource',
    'resourceId',
    'resourceName',
  ],

  data() {
    return {
      map: null,
      mapId: null,
      resizeObserver: null,
      mapPoints: [],
      mapAreas: [],
      loading: true,
      error: null,
      markers: [],
      polygons: [],
      // For overlapping polygon cycling
      lastClickPoint: null,
      currentOverlappingPolygons: [],
      currentOverlapIndex: 0,
      // Base URL for Nova
      novaBaseUrl: '/nova',
      // Base layers for terrain switcher
      baseLayers: {},
      layerControl: null
    }
  },

  mounted() {
    // Load Leaflet from CDN
    const script = document.createElement('script');
    script.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js';
    script.integrity = 'sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=';
    script.crossOrigin = '';

    // Add Leaflet CSS
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
    link.integrity = 'sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=';
    link.crossOrigin = '';
    document.head.appendChild(link);

    // Add custom CSS to ensure map container fills available space
    const styleEl = document.createElement('style');
    styleEl.textContent = `
      .leaflet-container {
        width: 100%;
        height: 100%;
      }
      .cycle-indicator {
        margin-top: 8px;
        padding: 4px 8px;
        background-color: rgba(0, 0, 0, 0.05);
        border-radius: 4px;
        font-size: 12px;
        color: #666;
        text-align: center;
      }
      .highlighted-polygon {
        animation: pulse 1.5s infinite;
      }
      @keyframes pulse {
        0% { opacity: 0.7; }
        50% { opacity: 1; }
        100% { opacity: 0.7; }
      }
      /* Layer control styling */
      .leaflet-control-layers {
        border-radius: 4px;
        box-shadow: 0 1px 5px rgba(0,0,0,0.2);
        background: white;
        border: none !important;
      }
      .leaflet-control-layers-toggle {
        background-size: 20px 20px;
      }
      .leaflet-control-layers-expanded {
        padding: 6px 10px 6px 6px;
        background: white;
        min-width: 150px;
      }
    `;
    document.head.appendChild(styleEl);

    // Initialize map after script loads
    script.onload = () => {
      try {
        // Initialize the map
        this.mapId = `map-${this.resourceId || Math.random().toString(36).substring(2, 9)}`;
        this.$refs.mapContainer.id = this.mapId;

        // Initialize the map with proper zoom controls and options
        this.map = L.map(this.mapId, {
          zoomControl: true,
          attributionControl: true,
          doubleClickZoom: true,  // Enable double-click zoom
          scrollWheelZoom: true,  // Enable scroll wheel zoom
          zoomDelta: 1,          // Zoom level change per zoom operation
          zoomSnap: 0.5,         // Snap to these zoom levels
          wheelPxPerZoomLevel: 120 // Adjust sensitivity of mouse wheel zoom
        }).setView([-37.8304, 144.9733], 13);

        // Define base layers for terrain switcher
        this.baseLayers = {
          'OpenStreetMap': L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            maxZoom: 19,
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          }),
          'Satellite': L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
            maxZoom: 19,
            attribution: '&copy; <a href="https://www.esri.com/">Esri</a>, Maxar, Earthstar Geographics, and the GIS User Community'
          }),
          'Terrain': L.tileLayer('https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png', {
            maxZoom: 17,
            attribution: '&copy; <a href="https://opentopomap.org">OpenTopoMap</a> (<a href="https://creativecommons.org/licenses/by-sa/3.0/">CC-BY-SA</a>)'
          }),
          'Grayscale': L.tileLayer('https://cartodb-basemaps-{s}.global.ssl.fastly.net/light_all/{z}/{x}/{y}.png', {
            maxZoom: 19,
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors, &copy; <a href="https://carto.com/attribution">CARTO</a>'
          })
        };

        // Add the default base layer (OpenStreetMap)
        this.baseLayers['OpenStreetMap'].addTo(this.map);

        // Add layer control to switch between base layers
        this.layerControl = L.control.layers(this.baseLayers, {}, {
          position: 'topright',
          collapsed: false
        }).addTo(this.map);

        // If we have a resourceId (we're on a detail page), fetch the map data
        if (this.resourceId) {
          this.fetchMapData();
        } else {
          // Add a default marker if we're not on a detail page
          L.marker([-37.8304, 144.9733]).addTo(this.map)
            .bindPopup('Shrine of Remembrance')
            .openPopup();
          this.loading = false;
        }

        // Force a resize after initialization
        setTimeout(() => {
          if (this.map) {
            this.map.invalidateSize();
          }
        }, 100);

        // Add event handlers to properly manage map interactions
        this.map.on('dblclick', (e) => {
          // We're just logging the event, not preventing default behavior
          console.log('Double-click detected at:', e.latlng);
        });

        this.map.on('error', (e) => {
          console.error('Leaflet map error:', e.error || e);
        });

        this.map.on('zoomanim', (e) => {
          console.log(`Zooming from level ${e.zoom} to ${e.center}`);
        });

        // Add click handler for cycling through overlapping polygons
        this.map.on('click', this.handleMapClick);

        this.setupResizeObserver();
      } catch (error) {
        console.error('Error initializing map:', error);
        this.error = `Error initializing map: ${error.message || 'Unknown error'}`;
        this.loading = false;
      }
    };

    document.head.appendChild(script);
  },

  methods: {
    // Generate Nova edit URL for a resource
    getNovaEditUrl(resourceType, resourceId) {
      return `${this.novaBaseUrl}/resources/${resourceType}/${resourceId}/edit`;
    },

    // Create an edit button HTML for popups
    createEditButton(resourceType, resourceId) {
      const editUrl = this.getNovaEditUrl(resourceType, resourceId);
      return `<div class="mt-2"><a href="${editUrl}" class="text-primary-500 hover:text-primary-700 underline text-sm">Edit</a></div>`;
    },

    // Helper method to check if a point is inside a polygon
    isPointInPolygon(point, polygon) {
      try {
        // First check if point is within the bounding box (fast check)
        if (!polygon.getBounds().contains(point)) {
          return false;
        }

        // Then use Leaflet's contains method if available
        if (typeof polygon.contains === 'function') {
          return polygon.contains(point);
        }

        // Fallback to a simpler check if contains is not available
        return true; // If it's in the bounding box, consider it contained
      } catch (e) {
        console.error('Error in isPointInPolygon:', e);
        return false;
      }
    },

    // Helper method to find all polygons that contain a point
    findPolygonsAtPoint(point) {
      if (!this.polygons || !this.polygons.length) return [];

      return this.polygons.filter(polygon =>
        this.isPointInPolygon(point, polygon)
      );
    },

    // Helper method to highlight the current polygon in a stack
    highlightPolygon(currentPolygon, allPolygons) {
      // Reset all polygons to their original style
      allPolygons.forEach(poly => {
        poly.setStyle({
          weight: poly._originalStyle?.weight || poly.options.weight || 2,
          opacity: poly._originalStyle?.opacity || poly.options.opacity || 0.7,
          className: ''
        });
      });

      // Store original style if not already stored
      if (!currentPolygon._originalStyle) {
        currentPolygon._originalStyle = {
          weight: currentPolygon.options.weight,
          opacity: currentPolygon.options.opacity
        };
      }

      // Highlight the current polygon
      currentPolygon.setStyle({
        weight: currentPolygon._originalStyle.weight + 2,
        opacity: 1,
        className: 'highlighted-polygon'
      });
    },

    // Handle map click for cycling through overlapping polygons
    handleMapClick(e) {
      console.log('Map clicked at:', e.latlng);
      const clickPoint = e.latlng;

      // Debug: Log all polygons
      console.log('Total polygons on map:', this.polygons.length);

      // Find all polygons that contain this point
      const overlappingPolygons = this.findPolygonsAtPoint(clickPoint);
      console.log('Overlapping polygons found:', overlappingPolygons.length);

      // If we have overlapping polygons
      if (overlappingPolygons.length > 0) {
        // Check if this is a new click location or the same as before
        const isSameLocation = this.lastClickPoint &&
                              this.lastClickPoint.distanceTo(clickPoint) < 10; // 10 pixels tolerance

        // If it's the same location and same polygons, cycle to the next polygon
        if (isSameLocation &&
            JSON.stringify(this.currentOverlappingPolygons.map(p => p._leaflet_id)) ===
            JSON.stringify(overlappingPolygons.map(p => p._leaflet_id))) {
          this.currentOverlapIndex = (this.currentOverlapIndex + 1) % overlappingPolygons.length;
        } else {
          // New location or different polygons, start with the first polygon
          this.currentOverlapIndex = 0;
          this.currentOverlappingPolygons = overlappingPolygons;
          this.lastClickPoint = clickPoint;
        }

        // Show the popup for the current polygon in the cycle
        const currentPolygon = overlappingPolygons[this.currentOverlapIndex];

        // Close any open popups
        this.map.closePopup();

        // Get the name and description for the popup
        const areaId = currentPolygon._areaId;
        const area = this.mapAreas.find(a => a.id === areaId);

        let name = 'Unnamed Area';
        let description = '';

        if (area) {
          const nameField = area.fields.find(field => field.attribute === 'name');
          const descField = area.fields.find(field => field.attribute === 'description');

          name = nameField ? nameField.value : 'Unnamed Area';
          description = descField ? descField.value : '';
        }

        // Add cycling indicator and edit button if there are multiple polygons
        let popupContent = `
          <strong>${name}</strong>
          ${description ? `<br>${description}` : ''}
        `;

        if (overlappingPolygons.length > 1) {
          popupContent += `<div class="cycle-indicator">(${this.currentOverlapIndex + 1}/${overlappingPolygons.length} - Click again to cycle)</div>`;
        }

        // Add edit button
        popupContent += this.createEditButton('map-areas', currentPolygon._areaId);

        L.popup()
          .setLatLng(clickPoint)
          .setContent(popupContent)
          .openOn(this.map);

        // Highlight the current polygon
        this.highlightPolygon(currentPolygon, overlappingPolygons);
      }
    },

    fetchMapData() {
      this.loading = true;
      this.error = null;

      // Start both fetch operations
      this.fetchMapPoints();
      this.fetchMapAreas();
    },

    fetchMapPoints() {
      console.log(`Fetching map points for ${this.resourceName} ID:${this.resourceId}`);

      // Check if we're on a project iteration resource
      if (this.resourceName === 'project-iterations') {
        this.fetchProjectIterationMapPoints();
        return;
      }

      // First try the card's own API endpoint for regular maps
      Nova.request().get(`/nova-vendor/compiled-map-card/map-points/${this.resourceId}`)
        .then(response => {
          console.log('Card API response for map points:', response.data);

          if (response.data && response.data.success && response.data.data && response.data.data.length > 0) {
            // Convert the API response to a format similar to Nova's
            this.mapPoints = response.data.data.map(point => ({
              id: point.id,
              fields: [
                { attribute: 'name', value: point.name },
                { attribute: 'description', value: point.description },
                { attribute: 'location', value: point.location }
              ]
            }));

            console.log(`Found ${this.mapPoints.length} map points from card API`);
            this.addMapPointsToMap();
          } else {
            console.warn('No map points found in card API response, trying Nova relationship API');
            this.fetchMapPointsViaRelationship();
          }
        })
        .catch(error => {
          console.error('Error fetching map points from card API:', error);
          console.log('Trying Nova relationship API as fallback...');
          this.fetchMapPointsViaRelationship();
        });
    },

    fetchMapPointsViaRelationship() {
      // Try to fetch map points via the Nova relationship API
      Nova.request().get(`/nova-api/${this.resourceName}/${this.resourceId}/relationships/mapPoints`)
        .then(response => {
          console.log('Nova relationship API response for map points:', response.data);

          if (response.data && response.data.resources && response.data.resources.length > 0) {
            this.mapPoints = response.data.resources;
            console.log(`Found ${this.mapPoints.length} map points via Nova relationship API`);
            this.addMapPointsToMap();
          } else {
            console.warn('No map points found in Nova relationship API response');
          }
        })
        .catch(error => {
          console.error('Error fetching map points via Nova relationship API:', error);
          this.loading = false;
        });
    },

    fetchMapAreas() {
      console.log(`Fetching map areas for ${this.resourceName} ID:${this.resourceId}`);

      // Check if we're on a project iteration resource
      if (this.resourceName === 'project-iterations') {
        this.fetchProjectIterationMapAreas();
        return;
      }

      // Use our custom API endpoint for map areas
      Nova.request().get(`/nova-vendor/compiled-map-card/map-areas/${this.resourceId}`)
        .then(response => {
          console.log('Card API response for map areas:', response.data);

          if (response.data && response.data.success && response.data.data && response.data.data.length > 0) {
            // Convert the API response to a format similar to Nova's
            this.mapAreas = response.data.data.map(area => ({
              id: area.id,
              style: area.style, // Include the style information from the API response
              fields: [
                { attribute: 'name', value: area.name },
                { attribute: 'description', value: area.description },
                { attribute: 'area', value: area.area }
              ]
            }));

            console.log(`Found ${this.mapAreas.length} map areas from card API`);
            this.addMapAreasToMap();
          } else {
            console.warn('No map areas found in card API response');
          }
          this.loading = false;
        })
        .catch(error => {
          console.error('Error fetching map areas from card API:', error);
          this.loading = false;
        });
    },

    fetchProjectIterationMapPoints() {
      console.log(`Fetching main map points for project iteration ID:${this.resourceId}`);

      Nova.request().get(`/nova-vendor/compiled-map-card/project-iteration-map-points/${this.resourceId}`)
        .then(response => {
          console.log('Project iteration map points API response:', response.data);

          if (response.data && response.data.success && response.data.data && response.data.data.length > 0) {
            // Convert the API response to a format similar to Nova's
            this.mapPoints = response.data.data.map(point => ({
              id: point.id,
              fields: [
                { attribute: 'name', value: point.name },
                { attribute: 'description', value: point.description },
                { attribute: 'location', value: point.location }
              ]
            }));

            console.log(`Found ${this.mapPoints.length} map points from main map (${response.data.map_name})`);
            this.addMapPointsToMap();
          } else {
            console.warn('No map points found for project iteration main map');
          }
        })
        .catch(error => {
          console.error('Error fetching project iteration map points:', error);
          this.loading = false;
        });
    },

    fetchProjectIterationMapAreas() {
      console.log(`Fetching main map areas for project iteration ID:${this.resourceId}`);

      Nova.request().get(`/nova-vendor/compiled-map-card/project-iteration-map-areas/${this.resourceId}`)
        .then(response => {
          console.log('Project iteration map areas API response:', response.data);

          if (response.data && response.data.success && response.data.data && response.data.data.length > 0) {
            // Convert the API response to a format similar to Nova's
            this.mapAreas = response.data.data.map(area => ({
              id: area.id,
              style: area.style, // Include the style information from the API response
              fields: [
                { attribute: 'name', value: area.name },
                { attribute: 'description', value: area.description },
                { attribute: 'area', value: area.area }
              ]
            }));

            console.log(`Found ${this.mapAreas.length} map areas from main map (${response.data.map_name})`);
            this.addMapAreasToMap();
          } else {
            console.warn('No map areas found for project iteration main map');
          }
          this.loading = false;
        })
        .catch(error => {
          console.error('Error fetching project iteration map areas:', error);
          this.loading = false;
        });
    },

    addMapPointsToMap() {
      if (!this.mapPoints || !this.mapPoints.length) {
        console.log('No map points to display');
        return;
      }

      console.log('Adding map points to map:', this.mapPoints);

      // Debug: Print all fields for each point
      this.mapPoints.forEach((point, index) => {
        console.log(`Map point ${index + 1} (ID: ${point.id}):`);
        if (point.fields) {
          point.fields.forEach(field => {
            console.log(`  Field: ${field.attribute}, Value:`, field.value);
          });
        } else {
          console.warn('  No fields found for this point');
        }
      });

      const bounds = L.latLngBounds();
      const markers = [];

      // Add each map point as a marker
      this.mapPoints.forEach(point => {
        try {
          // Extract location data from the point
          // The location field is stored as a Point in the database
          // We need to parse it to get the latitude and longitude
          let lat, lng;

          // Try to find the location field in the point's fields
          const locationField = point.fields.find(field => field.attribute === 'location');

          console.log(`Processing location for point ID: ${point.id}`);

          if (!locationField) {
            console.warn('No location field found for this point');
            return;
          }

          console.log('Location field found:', locationField);

          if (!locationField.value) {
            console.warn('Location field has no value');
            return;
          }

          // Parse the location value based on its format
          if (typeof locationField.value === 'string') {
            // If it's a string, try to parse it as JSON
            try {
              console.log('Location value is a string:', locationField.value);
              const locationObj = JSON.parse(locationField.value);
              console.log('Parsed location object:', locationObj);

              if (locationObj.coordinates) {
                lat = locationObj.coordinates[1]; // GeoJSON format is [lng, lat]
                lng = locationObj.coordinates[0];
                console.log(`Extracted from coordinates: lat=${lat}, lng=${lng}`);
              } else if (locationObj.lat !== undefined && locationObj.lng !== undefined) {
                lat = locationObj.lat;
                lng = locationObj.lng;
                console.log(`Extracted from lat/lng: lat=${lat}, lng=${lng}`);
              }
            } catch (e) {
              console.error('Error parsing location string:', e, locationField.value);
            }
          } else if (typeof locationField.value === 'object') {
            // If it's already an object
            const locationObj = locationField.value;
            console.log('Location value is an object:', locationObj);

            // Check different possible formats
            if (locationObj.coordinates) {
              // GeoJSON format
              lat = locationObj.coordinates[1]; // GeoJSON format is [lng, lat]
              lng = locationObj.coordinates[0];
              console.log(`Extracted from coordinates: lat=${lat}, lng=${lng}`);
            } else if (locationObj.lat !== undefined && locationObj.lng !== undefined) {
              // Simple {lat, lng} format
              lat = locationObj.lat;
              lng = locationObj.lng;
              console.log(`Extracted from lat/lng: lat=${lat}, lng=${lng}`);
            } else if (Array.isArray(locationObj) && locationObj.length >= 2) {
              // Array format [lng, lat] or [lat, lng]
              // Assume GeoJSON format [lng, lat] first
              lat = locationObj[1];
              lng = locationObj[0];
              console.log(`Extracted from array: lat=${lat}, lng=${lng}`);
            } else if (locationObj.type === 'Point' && Array.isArray(locationObj.value)) {
              // Nova MapPointField format
              lat = locationObj.value[1];
              lng = locationObj.value[0];
              console.log(`Extracted from Point value: lat=${lat}, lng=${lng}`);
            } else if (locationObj.latitude !== undefined && locationObj.longitude !== undefined) {
              // MatanYadaev\EloquentSpatial format
              lat = locationObj.latitude;
              lng = locationObj.longitude;
              console.log(`Extracted from latitude/longitude: lat=${lat}, lng=${lng}`);
            } else if (locationObj.srid && locationObj.wkt) {
              // WKT format
              console.log('WKT format detected:', locationObj.wkt);
              // Extract coordinates from WKT string like 'POINT(lng lat)'
              const match = locationObj.wkt.match(/POINT\(([\d.-]+)\s+([\d.-]+)\)/i);
              if (match) {
                lng = parseFloat(match[1]);
                lat = parseFloat(match[2]);
                console.log(`Extracted from WKT: lat=${lat}, lng=${lng}`);
              }
            }
          }

          // If we couldn't find or parse the location, skip this point
          if (!lat || !lng) {
            console.warn('Could not extract location from map point:', point);
            return;
          }

          console.log(`Extracted coordinates: lat=${lat}, lng=${lng}`);

          // Get the name and description for the popup
          const nameField = point.fields.find(field => field.attribute === 'name');
          const descField = point.fields.find(field => field.attribute === 'description');

          const name = nameField ? nameField.value : 'Unnamed Point';
          const description = descField ? descField.value : '';

          // Create the marker
          const marker = L.marker([lat, lng]).addTo(this.map);

          // Add popup with name, description, and edit button
          const popupContent = `
            <strong>${name}</strong>
            ${description ? `<br>${description}` : ''}
            ${this.createEditButton('map-points', point.id)}
          `;
          marker.bindPopup(popupContent);

          // Add to bounds for auto-zooming
          bounds.extend([lat, lng]);
          markers.push(marker);
        } catch (e) {
          console.error('Error adding map point:', e, point);
        }
      });

      // Store markers for later reference
      this.markers = markers;

      // Update the map view if we have markers
      this.updateMapView();
    },

    addMapAreasToMap() {
      if (!this.mapAreas || !this.mapAreas.length) {
        console.log('No map areas to display');
        return;
      }

      console.log('Adding map areas to map:', this.mapAreas);

      // Debug: Print all fields for each area
      this.mapAreas.forEach((area, index) => {
        console.log(`Map area ${index + 1} (ID: ${area.id}):`);
        if (area.fields) {
          area.fields.forEach(field => {
            console.log(`  Field: ${field.attribute}, Value:`, field.value);
          });
        } else {
          console.warn('  No fields found for this area');
        }
      });

      const bounds = this.map ? L.latLngBounds() : null;
      const polygons = [];

      // Add each map area as a polygon
      this.mapAreas.forEach(area => {
        try {
          // Extract area data from the area object
          // The area field is stored as a Polygon in the database
          // We need to parse it to get the coordinates
          let coordinates = null;

          // Try to find the area field in the area's fields
          const areaField = area.fields.find(field => field.attribute === 'area');

          console.log(`Processing area for area ID: ${area.id}`);

          if (!areaField) {
            console.warn('No area field found for this area');
            return;
          }

          console.log('Area field found:', areaField);

          if (!areaField.value) {
            console.warn('Area field has no value');
            return;
          }

          // Parse the area value based on its format
          if (typeof areaField.value === 'string') {
            // If it's a string, try to parse it as JSON
            try {
              console.log('Area value is a string:', areaField.value);
              const areaObj = JSON.parse(areaField.value);
              console.log('Parsed area object:', areaObj);

              if (areaObj.coordinates && Array.isArray(areaObj.coordinates)) {
                coordinates = areaObj.coordinates;
                console.log('Extracted coordinates from GeoJSON:', coordinates);
              }
            } catch (e) {
              console.error('Error parsing area string:', e, areaField.value);
            }
          } else if (typeof areaField.value === 'object') {
            // If it's already an object
            const areaObj = areaField.value;
            console.log('Area value is an object:', areaObj);

            // Check different possible formats
            if (areaObj.coordinates && Array.isArray(areaObj.coordinates)) {
              // GeoJSON format
              coordinates = areaObj.coordinates;
              console.log('Extracted coordinates from GeoJSON:', coordinates);
            } else if (areaObj.rings && Array.isArray(areaObj.rings)) {
              // ESRI format
              coordinates = areaObj.rings;
              console.log('Extracted rings from ESRI format:', coordinates);
            } else if (Array.isArray(areaObj) && areaObj.length > 0 && Array.isArray(areaObj[0])) {
              // Direct array of coordinates
              coordinates = areaObj;
              console.log('Using direct array of coordinates:', coordinates);
            } else if (areaObj.type === 'Polygon' && Array.isArray(areaObj.coordinates)) {
              // GeoJSON Polygon format
              coordinates = areaObj.coordinates;
              console.log('Extracted coordinates from GeoJSON Polygon:', coordinates);
            }
          }

          // If we couldn't find or parse the coordinates, skip this area
          if (!coordinates) {
            console.warn('Could not extract coordinates from map area:', area);
            return;
          }

          // Get the name and description for the popup
          const nameField = area.fields.find(field => field.attribute === 'name');
          const descField = area.fields.find(field => field.attribute === 'description');

          const name = nameField ? nameField.value : 'Unnamed Area';
          const description = descField ? descField.value : '';

          // Convert GeoJSON coordinates to Leaflet format
          // GeoJSON Polygon coordinates are in format [[[lng, lat], [lng, lat], ...]]
          // Leaflet expects [[lat, lng], [lat, lng], ...]
          let leafletCoords = [];

          console.log('Raw coordinates:', coordinates);

          try {
            // Handle different levels of nesting in the coordinates
            if (coordinates.length > 0 && Array.isArray(coordinates[0]) && Array.isArray(coordinates[0][0])) {
              // This is a polygon with holes or a multi-polygon: [[[lng, lat], ...], [[lng, lat], ...]]
              leafletCoords = coordinates.map(ring =>
                ring.map(coord => [coord[1], coord[0]]) // Convert [lng, lat] to [lat, lng]
              );
              console.log('Converted multi-ring polygon coordinates:', leafletCoords);
            } else if (coordinates.length > 0 && Array.isArray(coordinates[0]) && typeof coordinates[0][0] === 'number') {
              // This is a simple polygon: [[lng, lat], [lng, lat], ...]
              leafletCoords = coordinates.map(coord => [coord[1], coord[0]]);
              console.log('Converted simple polygon coordinates:', leafletCoords);
            } else {
              console.warn('Unexpected coordinates format:', coordinates);
              // Try to handle it anyway
              if (coordinates.length > 0) {
                if (Array.isArray(coordinates[0])) {
                  leafletCoords = [coordinates.map(coord =>
                    Array.isArray(coord) ? [coord[1], coord[0]] : coord
                  )];
                  console.log('Attempted conversion of unknown format:', leafletCoords);
                }
              }
            }
          } catch (e) {
            console.error('Error converting coordinates:', e);
            return;
          }

          console.log('Converted to Leaflet coordinates:', leafletCoords);

          // Only create the polygon if we have valid coordinates
          if (leafletCoords && leafletCoords.length > 0) {
            try {
              // Default styling
              const defaultStyle = {
                color: '#3388ff',
                weight: 2,
                opacity: 0.7,
                fillColor: '#3388ff',
                fillOpacity: 0.4,
                dashArray: null
              };

              // Override with custom styles if available
              const style = { ...defaultStyle };

              console.log('Area object:', area);
              console.log('Area style property:', area.style);

              // Enhanced debugging for style properties
              if (area.style) {
                console.log('Style properties details:');
                Object.entries(area.style).forEach(([key, value]) => {
                  console.log(`  ${key}: ${value} (${typeof value})`);
                });
              }

              if (area.style) {
                console.log('Style properties available:');
                console.log('fillColor:', area.style.fillColor);
                console.log('fillOpacity:', area.style.fillOpacity);
                console.log('color:', area.style.color);
                console.log('weight:', area.style.weight);
                console.log('opacity:', area.style.opacity);
                console.log('dashArray:', area.style.dashArray);

                if (area.style.fillColor) style.fillColor = area.style.fillColor;
                if (area.style.fillOpacity !== undefined) style.fillOpacity = parseFloat(area.style.fillOpacity);
                if (area.style.color) style.color = area.style.color;
                if (area.style.weight !== undefined) style.weight = parseInt(area.style.weight);
                if (area.style.opacity !== undefined) style.opacity = parseFloat(area.style.opacity);
                if (area.style.dashArray) style.dashArray = area.style.dashArray;
              } else {
                console.warn('No style property found on area object');
              }

              console.log('Applying polygon style:', style);

              // Create the polygon with the custom styling
              const polygon = L.polygon(leafletCoords, style).addTo(this.map);

              // Store the area ID on the polygon for reference
              polygon._areaId = area.id;

              // Store original content for popup with edit button
              polygon._popupContent = `
                <strong>${name}</strong>
                ${description ? `<br>${description}` : ''}
                ${this.createEditButton('map-areas', area.id)}
              `;

              // Add a direct click handler to each polygon that implements cycling
              polygon.on('click', (e) => {
                console.log('Polygon clicked directly:', area.id);

                // Find all polygons at this click point
                const clickPoint = e.latlng;
                const overlappingPolygons = this.findPolygonsAtPoint(clickPoint);
                console.log('Overlapping polygons found:', overlappingPolygons.length);

                // If we have multiple overlapping polygons, implement cycling
                if (overlappingPolygons.length > 1) {
                  // Check if this is a new click location or the same as before
                  const isSameLocation = this.lastClickPoint &&
                                        this.lastClickPoint.distanceTo(clickPoint) < 10; // 10 pixels tolerance

                  // If it's the same location and same polygons, cycle to the next polygon
                  if (isSameLocation &&
                      JSON.stringify(this.currentOverlappingPolygons.map(p => p._leaflet_id)) ===
                      JSON.stringify(overlappingPolygons.map(p => p._leaflet_id))) {
                    this.currentOverlapIndex = (this.currentOverlapIndex + 1) % overlappingPolygons.length;
                  } else {
                    // New location or different polygons, start with the first polygon
                    this.currentOverlapIndex = 0;
                    this.currentOverlappingPolygons = overlappingPolygons;
                    this.lastClickPoint = clickPoint;
                  }

                  // Show the popup for the current polygon in the cycle
                  const currentPolygon = overlappingPolygons[this.currentOverlapIndex];

                  // Close any open popups
                  this.map.closePopup();

                  // Get the name and description for the popup
                  const areaId = currentPolygon._areaId;
                  const currentArea = this.mapAreas.find(a => a.id === areaId);

                  let currentName = 'Unnamed Area';
                  let currentDescription = '';

                  if (currentArea) {
                    const nameField = currentArea.fields.find(field => field.attribute === 'name');
                    const descField = currentArea.fields.find(field => field.attribute === 'description');

                    currentName = nameField ? nameField.value : 'Unnamed Area';
                    currentDescription = descField ? descField.value : '';
                  }

                  // Add cycling indicator and edit button
                  let popupContent = `
                    <strong>${currentName}</strong>
                    ${currentDescription ? `<br>${currentDescription}` : ''}
                    <div class="cycle-indicator">(${this.currentOverlapIndex + 1}/${overlappingPolygons.length} - Click again to cycle)</div>
                    ${this.createEditButton('map-areas', currentPolygon._areaId)}
                  `;

                  L.popup()
                    .setLatLng(clickPoint)
                    .setContent(popupContent)
                    .openOn(this.map);

                  // Highlight the current polygon
                  this.highlightPolygon(currentPolygon, overlappingPolygons);
                } else {
                  // Only one polygon, just show its popup with edit button
                  let popupContent = `
                    <strong>${name}</strong>
                    ${description ? `<br>${description}` : ''}
                    ${this.createEditButton('map-areas', area.id)}
                  `;

                  L.popup()
                    .setLatLng(e.latlng)
                    .setContent(popupContent)
                    .openOn(this.map);
                }

                // Stop propagation to prevent the map click handler from firing
                L.DomEvent.stopPropagation(e);
              });

              // Add to bounds for auto-zooming
              if (bounds) {
                try {
                  const polygonBounds = polygon.getBounds();
                  bounds.extend(polygonBounds);
                } catch (e) {
                  console.error('Error extending bounds with polygon:', e);
                }
              }

              polygons.push(polygon);
              console.log('Successfully added polygon to map:', name);
            } catch (e) {
              console.error('Error creating polygon:', e, leafletCoords);
            }
          } else {
            console.warn('No valid coordinates for polygon:', name);
          }
        } catch (e) {
          console.error('Error adding map area:', e, area);
        }
      });

      // Store polygons for later reference
      this.polygons = polygons;

      // Update the map view
      this.updateMapView();
    },

    updateMapView() {
      // Only update the view if we have markers or polygons
      if ((!this.markers || this.markers.length === 0) &&
          (!this.polygons || this.polygons.length === 0)) {
        return;
      }

      // Create a bounds object that includes all markers and polygons
      const bounds = L.latLngBounds();

      // Add marker positions to bounds
      if (this.markers && this.markers.length > 0) {
        this.markers.forEach(marker => {
          bounds.extend(marker.getLatLng());
        });
      }

      // Add polygon bounds to bounds
      if (this.polygons && this.polygons.length > 0) {
        this.polygons.forEach(polygon => {
          bounds.extend(polygon.getBounds());
        });
      }

      // If we have any bounds, fit the map to them
      if (bounds.isValid()) {
        this.map.fitBounds(bounds, { padding: [50, 50] });

        // If there's only one marker and no polygons, zoom in a bit more
        if (this.markers.length === 1 && (!this.polygons || this.polygons.length === 0)) {
          this.map.setZoom(15);
        }
      }
    },

    setupResizeObserver() {
      // Use ResizeObserver if available to handle container size changes
      if (window.ResizeObserver) {
        this.resizeObserver = new ResizeObserver(() => {
          if (this.map) {
            this.map.invalidateSize();
          }
        });
        this.resizeObserver.observe(this.$refs.mapContainer);
      } else {
        // Fallback for browsers without ResizeObserver
        window.addEventListener('resize', () => {
          if (this.map) {
            this.map.invalidateSize();
          }
        });
      }
    }
  },

  beforeUnmount() {
    // Clean up
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }

    // Clean up markers
    if (this.markers && this.markers.length) {
      this.markers.forEach(marker => {
        if (marker && this.map) {
          marker.remove();
        }
      });
      this.markers = [];
    }

    // Clean up polygons
    if (this.polygons && this.polygons.length) {
      this.polygons.forEach(polygon => {
        if (polygon && this.map) {
          polygon.remove();
        }
      });
      this.polygons = [];
    }

    // Remove event handlers
    if (this.map) {
      this.map.off('click', this.handleMapClick);

      // Remove layer control if it exists
      if (this.layerControl) {
        this.layerControl.remove();
        this.layerControl = null;
      }

      // Clear base layers
      this.baseLayers = {};

      // Remove the map
      this.map.remove();
      this.map = null;
    }

    // Reset cycling state
    this.lastClickPoint = null;
    this.currentOverlappingPolygons = [];
    this.currentOverlapIndex = 0;
  }
}
</script>
